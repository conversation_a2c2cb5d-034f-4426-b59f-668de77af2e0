<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProviderController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\WebController;
use App\Http\Controllers\VideoConsultationController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\ProviderAvailabilityController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ReferralController;
use App\Http\Controllers\ManagementController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ShoppingCartController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\DigitalDownloadController;
use App\Http\Controllers\Admin\ProductManagementController;
use App\Http\Controllers\Admin\OrderManagementController;

Route::get('/', [WebController::class, 'index'])->name('home');

// Public routes - no authentication required
Route::get('terms-and-conditions', [WebController::class, 'termsAndConditions'])->name('terms-and-conditions');
Route::get('privacy-policy', [WebController::class, 'privacyPolicy'])->name('privacy-policy');

// Provider registration route - no authentication required
Route::get('providers/register', [\App\Http\Controllers\ProviderRegistrationController::class, 'showRegistrationPage'])->name('providers.register');

// Founder Club shareable link - no authentication required
Route::get('join-founders', [\App\Http\Controllers\Auth\RegisteredUserController::class, 'showFounderSignup'])->name('founder.signup');
Route::post('join-founders', [\App\Http\Controllers\Auth\RegisteredUserController::class, 'storeFounderSignup'])->name('founder.signup.store');

Route::get('dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Dashboard data routes for authenticated users
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard/data', [DashboardController::class, 'getDashboardData'])->name('dashboard.data');
    Route::get('provider/dashboard-data', [\App\Http\Controllers\ProviderDashboardController::class, 'getDashboardData'])->name('provider.dashboard.data');
    Route::get('management/dashboard/kpi', [\App\Http\Controllers\AnalyticsController::class, 'getKpiMetrics'])->name('management.dashboard.kpi');

    Route::get('provider/get-availability', [\App\Http\Controllers\ProviderAvailabilityController::class, 'getWeeklyAvailability'])->name('provider.availability.get');
    Route::get('provider/get-absences', [\App\Http\Controllers\ProviderAvailabilityController::class, 'getAbsences'])->name('provider.absences.get');
});

// Healthcare routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('providers/{provider}', [ProviderController::class, 'webShow'])->name('providers.show');

    Route::get('appointments', [AppointmentController::class, 'webIndex'])->name('appointments');
    Route::get('appointments/create', [AppointmentController::class, 'webCreate'])->name('appointments.create');
    Route::get('appointments/{appointment}', [AppointmentController::class, 'webShow'])->name('appointments.show');
    Route::get('appointments/{appointment}/edit', [AppointmentController::class, 'webEdit'])->name('appointments.edit');
    Route::get('appointments/{appointment}/payment', [PaymentController::class, 'showPaymentPage'])->name('appointments.payment');

    // Video consultation web routes (for webapp)
    Route::post('video/initialize/{appointmentId}', [VideoConsultationController::class, 'initializeSession'])->name('video.initialize');
    Route::get('video/session/{appointmentId}', [VideoConsultationController::class, 'getSessionData'])->name('video.session');
    Route::post('video/join/{appointmentId}', [VideoConsultationController::class, 'joinSession'])->name('video.join');
    Route::post('video/leave/{appointmentId}', [VideoConsultationController::class, 'leaveSession'])->name('video.leave');
    Route::get('video/status/{appointmentId}', [VideoConsultationController::class, 'getSessionStatus'])->name('video.status');
    Route::post('video/participant-disconnected/{appointmentId}', [VideoConsultationController::class, 'markParticipantDisconnected'])->name('video.participant-disconnected');
    Route::post('video/end/{appointmentId}', [VideoConsultationController::class, 'endSession'])->name('video.end');

    Route::get('chat', [ChatController::class, 'webIndex'])->name('chat');

    // Chat API routes for web (session-based authentication) - using different path to avoid API conflicts
    Route::prefix('web-api/chat')->group(function () {
        Route::post('start', [\App\Http\Controllers\AIChatController::class, 'startConversation'])->name('web.api.chat.start');
        Route::post('message', [\App\Http\Controllers\AIChatController::class, 'sendMessage'])->name('web.api.chat.message');
        Route::get('history', [\App\Http\Controllers\AIChatController::class, 'getHistory'])->name('web.api.chat.history');
        Route::get('conversation/{conversationId}', [\App\Http\Controllers\AIChatController::class, 'getConversation'])->name('web.api.chat.conversation');

        // Anonymous conversation transfer (session-based authentication)
        Route::post('transfer-anonymous', [ChatController::class, 'transferAnonymousConversation'])->name('web.api.chat.transfer-anonymous');
    });

    // Patient-specific routes
    Route::get('discover', [WebController::class, 'discover'])->name('discover');
    Route::get('shop', [WebController::class, 'shop'])->name('shop');
    Route::get('chat-history', [WebController::class, 'chatHistory'])->name('chat-history');
    Route::get('credit-history', [WebController::class, 'creditHistory'])->name('credit-history');

    // Chat utility routes
    Route::post('web-api/chat/update-titles', [App\Http\Controllers\ChatController::class, 'updateTitles']);




});



// Provider-specific routes
Route::middleware(['auth', 'verified', 'role:provider'])->prefix('provider')->group(function () {
    Route::get('availability', [ProviderController::class, 'availability'])->name('provider.availability');
    Route::get('services', [ProviderController::class, 'services'])->name('provider.services');
    Route::get('schedule', [ProviderController::class, 'schedule'])->name('provider.schedule');
    Route::get('patients', [ProviderController::class, 'patients'])->name('provider.patients');
    Route::get('earnings', [ProviderController::class, 'earnings'])->name('provider.earnings');
    Route::get('profile', [ProviderController::class, 'profile'])->name('provider.profile');

    // Provider product management
    Route::get('products', [ProviderController::class, 'products'])->name('provider.products')->middleware('permission:view products');
    Route::get('products/create', [ProductManagementController::class, 'create'])->name('provider.products.create')->middleware('permission:create products');
    Route::get('products/{id}', [ProductManagementController::class, 'show'])->name('provider.products.show')->middleware('permission:view products');
    Route::get('products/{id}/edit', [ProductManagementController::class, 'edit'])->name('provider.products.edit')->middleware('permission:edit products');
});

// Management routes - protected by role-based permissions
Route::middleware(['auth', 'verified'])->group(function () {
    // User management - requires 'view users' permission
    Route::get('users', [ManagementController::class, 'users'])->name('users')->middleware('permission:view users');

    // Patient management - requires 'view patients' permission
    Route::get('patients', [ManagementController::class, 'patients'])->name('patients')->middleware('permission:view patients');

    // Appointment management - requires 'view appointments' permission
    Route::get('manage/appointments', [ManagementController::class, 'appointments'])->name('appointments.manage')->middleware('permission:view appointments');

    // Payment management - requires 'view payments' permission
    Route::get('payments', [ManagementController::class, 'payments'])->name('payments')->middleware('permission:view payments');

    // Chat management - requires 'view chats' permission
    Route::get('chats', [ManagementController::class, 'chats'])->name('chats.manage')->middleware('permission:view chats');

    // Permission management - requires 'view permissions' permission
    Route::get('permissions', [ManagementController::class, 'permissions'])->name('permissions')->middleware('permission:view permissions');

    // Email template management - requires 'view email templates' permission
    Route::get('email-templates', [ManagementController::class, 'emailTemplates'])->name('email-templates')->middleware('permission:view email templates');

    // Notification management - requires 'view notifications' permission
    Route::get('notifications', [ManagementController::class, 'notifications'])->name('notifications')->middleware('permission:view notifications');

    // Waitlist management - requires 'view users' permission
    Route::get('waitlist', function () {
        return Inertia::render('Waitlist');
    })->name('waitlist')->middleware('permission:view users');

    // Service management - requires 'view services' permission
    Route::get('services', [ManagementController::class, 'services'])->name('services')->middleware('permission:view services');

    // System verification - requires admin permissions
    Route::get('system-verification', [ManagementController::class, 'systemVerification'])->name('system-verification')->middleware('permission:view users');

    // Referral management - requires 'view referrals' permission
    Route::get('referrals', [ManagementController::class, 'referrals'])->name('referrals')->middleware('permission:view referrals');

    // Credit management - requires 'view credits' permission
    Route::get('credits', [ManagementController::class, 'credits'])->name('credits')->middleware('permission:view credits');

    // Club management - requires 'view clubs' permission
    Route::get('clubs', [ManagementController::class, 'clubs'])->name('clubs')->middleware('permission:view clubs');

    // Ecommerce management routes
    Route::prefix('admin')->group(function () {
        // Product management - requires product permissions
        Route::get('products', [ProductManagementController::class, 'index'])->name('admin.products')->middleware('permission:view products');
        Route::get('products/create', [ProductManagementController::class, 'create'])->name('admin.products.create')->middleware('permission:create products');
        Route::post('products', [ProductManagementController::class, 'store'])->name('admin.products.store')->middleware('permission:create products');
        Route::get('products/{id}', [ProductManagementController::class, 'show'])->name('admin.products.show')->middleware('permission:view products');
        Route::get('products/{id}/edit', [ProductManagementController::class, 'edit'])->name('admin.products.edit')->middleware('permission:edit products');
        Route::put('products/{id}', [ProductManagementController::class, 'update'])->name('admin.products.update')->middleware('permission:edit products');
        Route::delete('products/{id}', [ProductManagementController::class, 'destroy'])->name('admin.products.destroy')->middleware('permission:delete products');
        Route::patch('products/{id}/toggle-status', [ProductManagementController::class, 'toggleStatus'])->name('admin.products.toggle-status')->middleware('permission:edit products');

        // Order management - requires 'manage orders' permission or admin role
        Route::get('orders', [OrderManagementController::class, 'index'])->name('admin.orders');
        Route::get('orders/{id}', [OrderManagementController::class, 'show'])->name('admin.orders.show');
        Route::patch('orders/{id}/status', [OrderManagementController::class, 'updateStatus'])->name('admin.orders.update-status');
        Route::patch('orders/{id}/payment-status', [OrderManagementController::class, 'updatePaymentStatus'])->name('admin.orders.update-payment-status');
        Route::post('orders/{id}/notes', [OrderManagementController::class, 'addNote'])->name('admin.orders.add-note');
        Route::get('orders/stats', [OrderManagementController::class, 'getOrderStats'])->name('admin.orders.stats');
        Route::get('orders/export', [OrderManagementController::class, 'exportOrders'])->name('admin.orders.export');
    });
});

// API routes for web application (session-based authentication)
Route::middleware(['auth', 'verified'])->group(function () {
    // Services management routes (following availability pattern)
    Route::get('services-list', [ServiceController::class, 'index']);
    Route::post('save-service', [ServiceController::class, 'store']);
    Route::put('save-service/{id}', [ServiceController::class, 'update']);
    Route::delete('delete-service/{id}', [ServiceController::class, 'destroy']);
    Route::get('get-service-categories', [ServiceController::class, 'getCategories']);

    // Providers API routes (clean URLs for Vue rendering)
    Route::get('providers', [ManagementController::class, 'providers'])->name('providers')->middleware('permission:view providers');
    Route::get('providers/{id}', [ProviderController::class, 'show']);

    // Providers management routes (following availability pattern)
    Route::get('providers-list', [ProviderController::class, 'index']);
    Route::get('get-providers', [ProviderController::class, 'index']);
    Route::get('get-providers/{id}', [ProviderController::class, 'show']);
    Route::post('save-provider', [ProviderController::class, 'store']);
    Route::post('save-provider-with-user', [ProviderController::class, 'storeWithUser']);
    Route::put('update-provider/{id}', [ProviderController::class, 'update']);
    Route::delete('delete-provider/{id}', [ProviderController::class, 'destroy']);

    // Users management routes
    Route::get('users-list', [\App\Http\Controllers\UserController::class, 'index']);
    Route::get('users-without-provider', [\App\Http\Controllers\UserController::class, 'getUsersWithoutProvider']);

    // Patients management routes
    Route::get('patients-list', [\App\Http\Controllers\PatientController::class, 'index']);
    Route::post('save-patient', [\App\Http\Controllers\PatientController::class, 'store']);
    Route::put('update-patient/{id}', [\App\Http\Controllers\PatientController::class, 'update']);

    // Debug route to check user permissions (can be removed in production)
    Route::get('debug-permissions', [UserController::class, 'debugPermissions'])->middleware('auth');

    // Clinics management routes
    Route::get('clinics', [ManagementController::class, 'clinics'])->name('clinics')->middleware('permission:view clinics');
    Route::get('clinics-list', [\App\Http\Controllers\ClinicController::class, 'index']);
    Route::post('save-clinic', [\App\Http\Controllers\ClinicController::class, 'store']);
    Route::get('get-clinic/{id}', [\App\Http\Controllers\ClinicController::class, 'show']);
    Route::put('update-clinic/{id}', [\App\Http\Controllers\ClinicController::class, 'update']);
    Route::delete('delete-clinic/{id}', [\App\Http\Controllers\ClinicController::class, 'destroy']);
    Route::get('clinic-stats/{id}', [\App\Http\Controllers\ClinicController::class, 'getStats']);
    Route::get('clinic-users/{id}', [\App\Http\Controllers\ClinicController::class, 'getUsers']);

    // Payments management routes
    Route::get('payments-list', [\App\Http\Controllers\PaymentController::class, 'index']);
    Route::get('payments-detail/{id}', [\App\Http\Controllers\PaymentController::class, 'show']);

    // Chats management routes
    Route::get('chats-list', [\App\Http\Controllers\ChatController::class, 'managementIndex']);
    Route::get('chats-stats', [\App\Http\Controllers\ChatController::class, 'getStats']);
    Route::get('chats-detail/{id}', [\App\Http\Controllers\ChatController::class, 'managementShow']);
    Route::get('chats-messages/{id}', [\App\Http\Controllers\ChatController::class, 'getMessages']);
    Route::post('chats-flag/{id}', [\App\Http\Controllers\ChatController::class, 'flagChat']);
    Route::post('chats-unflag/{id}', [\App\Http\Controllers\ChatController::class, 'unflagChat']);
    Route::post('chats-archive/{id}', [\App\Http\Controllers\ChatController::class, 'archiveChat']);
    Route::post('chats-add-message/{id}', [\App\Http\Controllers\ChatController::class, 'addManagementMessage']);

    // Permissions management routes
    Route::get('roles-list', [\App\Http\Controllers\Auth\PermissionController::class, 'getRoles']);
    Route::get('permissions-list', [\App\Http\Controllers\Auth\PermissionController::class, 'index']);

    // Email templates routes (session-based authentication for web app)
    Route::get('email-templates-list', [\App\Http\Controllers\EmailTemplateController::class, 'index']);
    Route::get('email-templates/{id}', [\App\Http\Controllers\EmailTemplateController::class, 'show']);
    Route::post('email-templates', [\App\Http\Controllers\EmailTemplateController::class, 'store']);
    Route::put('email-templates/{id}', [\App\Http\Controllers\EmailTemplateController::class, 'update']);
    Route::delete('email-templates/{id}', [\App\Http\Controllers\EmailTemplateController::class, 'destroy']);
    Route::post('email-templates/{id}/test', [\App\Http\Controllers\EmailTemplateController::class, 'sendTestEmail']);
    Route::post('test-email-config', [\App\Http\Controllers\EmailTemplateController::class, 'testEmailConfiguration']);

    // System verification routes
    Route::post('verify-transaction-system', [\App\Http\Controllers\SystemVerificationController::class, 'verifyTransactionSystem']);
    Route::post('verify-anonymous-chat-mapping', [\App\Http\Controllers\SystemVerificationController::class, 'verifyAnonymousChatMapping']);

    // Notifications routes (session-based authentication for web app)
    Route::get('notifications-list', [\App\Http\Controllers\NotificationTemplateController::class, 'index']);
    Route::get('notifications/{id}', [\App\Http\Controllers\NotificationTemplateController::class, 'show']);
    Route::put('notifications/{id}', [\App\Http\Controllers\NotificationTemplateController::class, 'update']);

    // Enhanced notification management routes
    Route::post('notifications/send-by-device-type', [\App\Http\Controllers\NotificationManagementController::class, 'sendByDeviceType']);
    Route::post('notifications/send-by-browser', [\App\Http\Controllers\NotificationManagementController::class, 'sendByBrowser']);
    Route::post('notifications/send-by-platform', [\App\Http\Controllers\NotificationManagementController::class, 'sendByPlatform']);

    // Referrals routes (session-based authentication for web app)
    Route::get('referrals-code', [ReferralController::class, 'getReferralCode']);
    Route::post('send-referral-invite', [ReferralController::class, 'createReferral']);
    Route::get('referrals-my', [ReferralController::class, 'getUserReferrals']);
    Route::get('referrals-list', [ReferralController::class, 'index']);
    Route::get('referrals-stats', [ReferralController::class, 'getStats']);

    // Credits routes (session-based authentication for web app)
    Route::get('credits-list', [\App\Http\Controllers\UserCreditController::class, 'index']);
    Route::get('credits-balance', [\App\Http\Controllers\UserCreditController::class, 'getCreditBalance']);
    Route::get('credits-transactions', [\App\Http\Controllers\UserCreditController::class, 'getTransactionHistory']);
    Route::get('credits-all-transactions', [\App\Http\Controllers\UserCreditController::class, 'getAllCreditTransactions']);
    Route::get('credits-stats', [\App\Http\Controllers\UserCreditController::class, 'getCreditsStats']);
    Route::post('credits-add', [\App\Http\Controllers\UserCreditController::class, 'addCredits']);
    Route::get('users-search', [\App\Http\Controllers\UserController::class, 'index']);

    // Clubs routes (session-based authentication for web app)
    Route::get('clubs-list', [\App\Http\Controllers\Admin\ClubManagementController::class, 'getClubMembers']);
    Route::get('clubs-stats', [\App\Http\Controllers\Admin\ClubManagementController::class, 'getClubStats']);
    Route::get('clubs-codes', [\App\Http\Controllers\Admin\ClubManagementController::class, 'getFounderCodes']);

    // Waitlist management routes
    Route::get('waitlist-stats', [\App\Http\Controllers\Admin\WaitlistController::class, 'getStats']);
    Route::get('waitlist-analytics', [\App\Http\Controllers\Admin\WaitlistController::class, 'getSignupAnalytics']);
    Route::post('waitlist-toggle', [\App\Http\Controllers\Admin\WaitlistController::class, 'toggleWaitlistMode']);

    // Admin ecommerce API routes (session-based authentication for web app)
    Route::prefix('admin')->group(function () {
        // Product management API
        Route::get('products-list', [ProductManagementController::class, 'index']);
        Route::post('save-product', [ProductManagementController::class, 'store']);
        Route::put('save-product/{id}', [ProductManagementController::class, 'update']);
        Route::delete('delete-product/{id}', [ProductManagementController::class, 'destroy']);
        Route::patch('toggle-product-status/{id}', [ProductManagementController::class, 'toggleStatus']);

        // Bulk import routes
        Route::get('products/import-template', [ProductManagementController::class, 'downloadImportTemplate']);
        Route::post('products/validate-import', [ProductManagementController::class, 'validateImport']);
        Route::post('products/bulk-import', [ProductManagementController::class, 'bulkImport']);

        // Order management API
        Route::get('orders-list', [OrderManagementController::class, 'index']);
        Route::get('order-details/{id}', [OrderManagementController::class, 'show']);
        Route::patch('update-order-status/{id}', [OrderManagementController::class, 'updateStatus']);
        Route::patch('update-payment-status/{id}', [OrderManagementController::class, 'updatePaymentStatus']);
        Route::post('add-order-note/{id}', [OrderManagementController::class, 'addNote']);
        Route::get('order-stats', [OrderManagementController::class, 'getOrderStats']);
        Route::get('export-orders', [OrderManagementController::class, 'exportOrders']);
    });

    // Waitlist invitation management routes
    Route::get('waitlist-requests', [\App\Http\Controllers\WaitlistController::class, 'getWaitlistRequests']);
    Route::post('waitlist-requests/{requestId}/invite', [\App\Http\Controllers\WaitlistController::class, 'sendInvitation']);
    Route::post('waitlist-requests/bulk-invite', [\App\Http\Controllers\WaitlistController::class, 'sendBulkInvitations']);
    Route::post('send-bulk-invitations', [\App\Http\Controllers\WaitlistController::class, 'sendBulkInvitations']);
    Route::get('waitlist-invitation-stats', [\App\Http\Controllers\WaitlistController::class, 'getInvitationStats']);

    // Club invitation routes
    Route::post('send-club-invitation', [\App\Http\Controllers\Admin\ClubManagementController::class, 'sendClubInvitation']);
    Route::post('send-bulk-club-invitations', [\App\Http\Controllers\Admin\ClubManagementController::class, 'sendBulkClubInvitations']);

    // Provider availability API routes
    Route::middleware('role:provider')->prefix('provider')->group(function () {
        Route::get('get-availability', [ProviderAvailabilityController::class, 'getWeeklyAvailability']);
        Route::post('save-availability', [ProviderAvailabilityController::class, 'postWeeklyAvailability']);
        Route::put('save-availability', [ProviderAvailabilityController::class, 'updateWeeklyAvailability']);
        Route::get('availability-data', [ProviderAvailabilityController::class, 'availability']);

        // Provider absences routes
        Route::get('get-absences', [ProviderAvailabilityController::class, 'getAbsences']);
        Route::post('absences', [ProviderAvailabilityController::class, 'postAbsences']);
        Route::put('absences', [ProviderAvailabilityController::class, 'updateAbsences']);

        // Provider dashboard data
        Route::get('get-dashboard-data', [ProviderController::class, 'getDashboardData']);
        Route::get('get-appointments', [ProviderController::class, 'getAppointments']);
        Route::get('get-patients', [ProviderController::class, 'getPatients']);

        // Provider profile
        Route::get('get-profile', [ProviderController::class, 'getProfile']);
        Route::post('profile', [ProviderController::class, 'createOrUpdateProfile']);

        // Provider product management API
        Route::get('products-list', [ProductManagementController::class, 'index']);
        Route::post('save-product', [ProductManagementController::class, 'store']);
        Route::put('save-product/{id}', [ProductManagementController::class, 'update']);
        Route::delete('delete-product/{id}', [ProductManagementController::class, 'destroy']);
        Route::patch('toggle-product-status/{id}', [ProductManagementController::class, 'toggleStatus']);

        // Provider bulk import routes
        Route::get('products/import-template', [ProductManagementController::class, 'downloadImportTemplate']);
        Route::post('products/validate-import', [ProductManagementController::class, 'validateImport']);
        Route::post('products/bulk-import', [ProductManagementController::class, 'bulkImport']);

        // Provider earnings
        Route::get('get-earnings', [ProviderController::class, 'getDashboardData']);
    });

    // Management dashboard API routes - KPI route is defined earlier without permission restriction

    // Appointment management routes (following availability and services pattern)
    Route::get('appointments-list', [AppointmentController::class, 'userAppointments']);
    Route::get('get-appointments', [AppointmentController::class, 'userAppointments']);
    Route::post('save-appointment', [AppointmentController::class, 'store']);
    Route::put('save-appointment/{id}', [AppointmentController::class, 'update']);
    Route::delete('delete-appointment/{id}', [AppointmentController::class, 'destroy']);

    // Provider routes (following standard pattern)
    Route::get('get-providers/{id}/services', [ServiceController::class, 'index']);
    Route::get('get-providers/{providerId}/available-slots', [ProviderAvailabilityController::class, 'getAvailableTimeSlots']);

    // Payment routes (following standard pattern)
    Route::post('save-appointment-with-payment', [PaymentController::class, 'createAppointmentWithPayment']);
    Route::post('api/appointments/with-payment', [PaymentController::class, 'createAppointmentWithPayment']);
    Route::post('process-web-payment', [PaymentController::class, 'processWebPayment']);
    Route::post('confirm-elements-payment', [PaymentController::class, 'confirmElementsPayment']);

    Route::get('api/appointments/{id}', [AppointmentController::class, 'show']);

    // Ecommerce routes (session-based authentication for web app)
    Route::prefix('shop')->group(function () {
        // Product routes
        Route::get('products', [ProductController::class, 'index']);
        Route::get('products/{slug}', [ProductController::class, 'show']);
        Route::get('categories', [ProductController::class, 'categories']);
        Route::get('featured-products', [ProductController::class, 'featured']);
        Route::get('search-products', [ProductController::class, 'search']);

        // Shopping cart routes
        Route::get('cart', [ShoppingCartController::class, 'index']);
        Route::post('cart/add', [ShoppingCartController::class, 'add']);
        Route::put('cart/{productId}', [ShoppingCartController::class, 'update']);
        Route::delete('cart/{productId}', [ShoppingCartController::class, 'remove']);
        Route::delete('cart', [ShoppingCartController::class, 'clear']);
        Route::get('cart/count', [ShoppingCartController::class, 'count']);

        // Order routes
        Route::get('orders', [OrderController::class, 'index']);
        Route::get('orders/{orderNumber}', [OrderController::class, 'show']);
        Route::post('orders/{orderNumber}/cancel', [OrderController::class, 'cancel']);
        Route::get('checkout', [OrderController::class, 'showCheckout']);
        Route::post('checkout', [OrderController::class, 'checkout']);
        Route::post('orders/{orderNumber}/confirm-payment', [OrderController::class, 'confirmPayment']);

        // Digital downloads
        Route::get('downloads', [DigitalDownloadController::class, 'index']);
        Route::get('downloads/user', [DigitalDownloadController::class, 'userDownloads']);
        Route::get('downloads/{token}', [DigitalDownloadController::class, 'show']);
    });

    // Public digital download route (no auth required)
    Route::get('download/{token}', [DigitalDownloadController::class, 'download'])->name('digital-download');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
